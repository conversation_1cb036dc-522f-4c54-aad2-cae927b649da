{ "pid": 11572, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 11572, "tid": 1, "ts": 1752046990835166, "dur": 3181, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 11572, "tid": 1, "ts": 1752046990838351, "dur": 38476, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 11572, "tid": 1, "ts": 1752046990876831, "dur": 458880, "ph": "X", "name": "Write<PERSON><PERSON>", "args": {} },
{ "pid": 11572, "tid": 4268, "ts": 1752046991554003, "dur": 31, "ph": "X", "name": "", "args": {} },
{ "pid": 11572, "tid": 68719476736, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 11572, "tid": 68719476736, "ts": 1752046990835122, "dur": 569853, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 11572, "tid": 68719476736, "ts": 1752046991404977, "dur": 148310, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 11572, "tid": 68719476736, "ts": 1752046991405001, "dur": 934, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 11572, "tid": 68719476736, "ts": 1752046991405942, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11572, "tid": 68719476736, "ts": 1752046991405945, "dur": 104998, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11572, "tid": 68719476736, "ts": 1752046991510962, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 68719476736, "ts": 1752046991510969, "dur": 98, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 68719476736, "ts": 1752046991511076, "dur": 16, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 11572, "tid": 68719476736, "ts": 1752046991511095, "dur": 3863, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 11572, "tid": 68719476736, "ts": 1752046991514968, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 68719476736, "ts": 1752046991514974, "dur": 125, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 68719476736, "ts": 1752046991515107, "dur": 6, "ph": "X", "name": "ProcessMessages 455", "args": {} },
{ "pid": 11572, "tid": 68719476736, "ts": 1752046991515116, "dur": 83, "ph": "X", "name": "ReadAsync 455", "args": {} },
{ "pid": 11572, "tid": 68719476736, "ts": 1752046991515204, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 11572, "tid": 68719476736, "ts": 1752046991515207, "dur": 1808, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11572, "tid": 68719476736, "ts": 1752046991517019, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 68719476736, "ts": 1752046991517022, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 68719476736, "ts": 1752046991517081, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 11572, "tid": 68719476736, "ts": 1752046991517084, "dur": 7511, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11572, "tid": 68719476736, "ts": 1752046991524611, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 68719476736, "ts": 1752046991524615, "dur": 107, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 68719476736, "ts": 1752046991524731, "dur": 5, "ph": "X", "name": "ProcessMessages 13", "args": {} },
{ "pid": 11572, "tid": 68719476736, "ts": 1752046991524738, "dur": 28542, "ph": "X", "name": "ReadAsync 13", "args": {} },
{ "pid": 11572, "tid": 4268, "ts": 1752046991554039, "dur": 103, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ph": "M", "name": "thread_name", "args": { "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync" } },
{ "pid": 11572, "tid": 64424509440, "ts": 1752046990835058, "dur": 500693, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752046991335753, "dur": 69192, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {} },
{ "pid": 11572, "tid": 64424509440, "ts": 1752046991404952, "dur": 98, "ph": "X", "name": "WriteDagReadyMessage", "args": {} },
{ "pid": 11572, "tid": 4268, "ts": 1752046991554146, "dur": 18, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {} },
{ "pid": 11572, "tid": 60129542144, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 11572, "tid": 60129542144, "ts": 1752046990830050, "dur": 723283, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 11572, "tid": 60129542144, "ts": 1752046990830204, "dur": 3945, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 11572, "tid": 60129542144, "ts": 1752046991553338, "dur": 14, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 11572, "tid": 60129542144, "ts": 1752046991553361, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {} },
{ "pid": 11572, "tid": 4268, "ts": 1752046991554168, "dur": 18, "ph": "X", "name": "BuildAsync", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1752046991405380, "dur":54, "ph":"X", "name": "IPC_Client_InitializeAndConnectToParent",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752046991405504, "dur":108346, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752046991513865, "dur":443, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752046991514348, "dur":1274, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752046991515670, "dur":102, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752046991515772, "dur":7726, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752046991523499, "dur":512, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752046991524012, "dur":483, "ph":"X", "name": "ThreadStateDestroy",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752046991524623, "dur":50, "ph":"X", "name": "ThreadStateDestroy",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752046991524673, "dur":185, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752046991525331, "dur":22234, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1752046991516241, "dur":7239, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752046991515953, "dur":7543, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752046991515927, "dur":7592, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752046991515708, "dur":73, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752046991515793, "dur":1894, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"BuildPlayerDataGenerator Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":4, "ts":1752046991517748, "dur":5752, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752046991515880, "dur":2053, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752046991522502, "dur":897, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":5, "ts":1752046991517934, "dur":5471, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752046991516223, "dur":7267, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752046991516044, "dur":7465, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752046991516131, "dur":7374, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752046991516191, "dur":7324, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752046991516463, "dur":7049, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752046991516349, "dur":7168, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752046991516554, "dur":6973, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752046991516650, "dur":6919, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752046991516739, "dur":6744, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752046991516848, "dur":6674, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752046991516956, "dur":6585, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752046991517094, "dur":6453, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752046991517209, "dur":6320, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752046991517280, "dur":6207, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752046991517375, "dur":6118, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752046991517421, "dur":6137, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752046991517539, "dur":5985, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752046991517657, "dur":5845, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752046991517746, "dur":5788, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752046991553174, "dur":295, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 11572, "tid": 4268, "ts": 1752046991554424, "dur": 3382, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 11572, "tid": 4268, "ts": 1752046991557893, "dur": 7376, "ph": "X", "name": "backend1.traceevents", "args": {} },
{ "pid": 11572, "tid": 4268, "ts": 1752046991553998, "dur": 11368, "ph": "X", "name": "Write chrome-trace events", "args": {} },
