{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1752047081716296, "dur":55, "ph":"X", "name": "IPC_Client_InitializeAndConnectToParent",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752047081716403, "dur":135063, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752047081851478, "dur":5631, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752047081857518, "dur":512, "ph":"X", "name": "<PERSON><PERSON>",  "args": { "detail":"PrepareNodes" }}
,{ "pid":12345, "tid":0, "ts":1752047081858031, "dur":2315, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752047081860433, "dur":59, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Player" }}
,{ "pid":12345, "tid":0, "ts":1752047081860509, "dur":55, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/data.unity3d" }}
,{ "pid":12345, "tid":0, "ts":1752047081860576, "dur":81, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/resources.resource" }}
,{ "pid":12345, "tid":0, "ts":1752047081860700, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/ScriptingAssemblies.json" }}
,{ "pid":12345, "tid":0, "ts":1752047081860801, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/boot.config" }}
,{ "pid":12345, "tid":0, "ts":1752047081860898, "dur":74, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/boot.config_x4e9.info" }}
,{ "pid":12345, "tid":0, "ts":1752047081861240, "dur":91, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Assembly-CSharp-FeaturesChecked.txt_ki5r.info" }}
,{ "pid":12345, "tid":0, "ts":1752047081861506, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/DOTweenPro-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1752047081861575, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/DOTweenPro-FeaturesChecked.txt_dxgf.info" }}
,{ "pid":12345, "tid":0, "ts":1752047081861706, "dur":72, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/mscorlib-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1752047081861789, "dur":56, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/mscorlib-FeaturesChecked.txt_pwj8.info" }}
,{ "pid":12345, "tid":0, "ts":1752047081861893, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/System.Configuration-FeaturesChecked.txt_vfp9.info" }}
,{ "pid":12345, "tid":0, "ts":1752047081861989, "dur":68, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/System.Core-FeaturesChecked.txt_k2yd.info" }}
,{ "pid":12345, "tid":0, "ts":1752047081862065, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/System-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1752047081862161, "dur":65, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/System.Xml-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1752047081862324, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.Burst-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1752047081862383, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.Burst-FeaturesChecked.txt_5voi.info" }}
,{ "pid":12345, "tid":0, "ts":1752047081862474, "dur":66, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.Burst.Unsafe-FeaturesChecked.txt_9x4w.info" }}
,{ "pid":12345, "tid":0, "ts":1752047081862582, "dur":57, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.Cinemachine-FeaturesChecked.txt_blar.info" }}
,{ "pid":12345, "tid":0, "ts":1752047081862713, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.InputSystem-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1752047081862783, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.InputSystem-FeaturesChecked.txt_ivdb.info" }}
,{ "pid":12345, "tid":0, "ts":1752047081862877, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.InputSystem.ForUI-FeaturesChecked.txt_1qsl.info" }}
,{ "pid":12345, "tid":0, "ts":1752047081862935, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.Mathematics-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1752047081863047, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.Postprocessing.Runtime-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1752047081863115, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.Postprocessing.Runtime-FeaturesChecked.txt_6gzn.info" }}
,{ "pid":12345, "tid":0, "ts":1752047081863209, "dur":69, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.RenderPipelines.Core.Runtime-FeaturesChecked.txt_9rh7.info" }}
,{ "pid":12345, "tid":0, "ts":1752047081863410, "dur":68, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.AccessibilityModule-FeaturesChecked.txt_ttts.info" }}
,{ "pid":12345, "tid":0, "ts":1752047081863519, "dur":59, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.AIModule-FeaturesChecked.txt_jr17.info" }}
,{ "pid":12345, "tid":0, "ts":1752047081863632, "dur":67, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.AndroidJNIModule-FeaturesChecked.txt_x3ln.info" }}
,{ "pid":12345, "tid":0, "ts":1752047081863706, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.AnimationModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1752047081863885, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.AssetBundleModule-FeaturesChecked.txt_1gjh.info" }}
,{ "pid":12345, "tid":0, "ts":1752047081863958, "dur":56, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.AudioModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1752047081864092, "dur":78, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.CoreModule-FeaturesChecked.txt_uimb.info" }}
,{ "pid":12345, "tid":0, "ts":1752047081864218, "dur":58, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.DirectorModule-FeaturesChecked.txt_ruda.info" }}
,{ "pid":12345, "tid":0, "ts":1752047081864317, "dur":67, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine-FeaturesChecked.txt_x1ug.info" }}
,{ "pid":12345, "tid":0, "ts":1752047081864391, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.GridModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1752047081864484, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.HierarchyCoreModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1752047081864591, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.IMGUIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1752047081864672, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.IMGUIModule-FeaturesChecked.txt_ufk2.info" }}
,{ "pid":12345, "tid":0, "ts":1752047081864847, "dur":58, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.InputLegacyModule-FeaturesChecked.txt_y5my.info" }}
,{ "pid":12345, "tid":0, "ts":1752047081864946, "dur":70, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.InputModule-FeaturesChecked.txt_xblj.info" }}
,{ "pid":12345, "tid":0, "ts":1752047081865093, "dur":71, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.ParticleSystemModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1752047081865213, "dur":74, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.Physics2DModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1752047081865379, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.PhysicsModule-FeaturesChecked.txt_2l1t.info" }}
,{ "pid":12345, "tid":0, "ts":1752047081865441, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.PropertiesModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1752047081865569, "dur":81, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.SharedInternalsModule-FeaturesChecked.txt_sm9d.info" }}
,{ "pid":12345, "tid":0, "ts":1752047081865729, "dur":68, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.SubsystemsModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1752047081865838, "dur":65, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.TerrainModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1752047081865945, "dur":68, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.TerrainPhysicsModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1752047081866057, "dur":68, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.TextCoreFontEngineModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1752047081866207, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.TextCoreTextEngineModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1752047081866302, "dur":59, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.TextRenderingModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1752047081866402, "dur":67, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.TilemapModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1752047081866509, "dur":70, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.UI-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1752047081866653, "dur":71, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UIElementsModule-FeaturesChecked.txt_aabo.info" }}
,{ "pid":12345, "tid":0, "ts":1752047081866732, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.UIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1752047081866825, "dur":69, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.UnityAnalyticsCommonModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1752047081867022, "dur":70, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UnityAnalyticsModule-FeaturesChecked.txt_cjxw.info" }}
,{ "pid":12345, "tid":0, "ts":1752047081867134, "dur":70, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UnityWebRequestModule-FeaturesChecked.txt_jk15.info" }}
,{ "pid":12345, "tid":0, "ts":1752047081867278, "dur":73, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.VFXModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1752047081867358, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.VFXModule-FeaturesChecked.txt_r8mm.info" }}
,{ "pid":12345, "tid":0, "ts":1752047081867517, "dur":82, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.VRModule-FeaturesChecked.txt_nz9x.info" }}
,{ "pid":12345, "tid":0, "ts":1752047081867606, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.XRModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1752047081867719, "dur":57, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/jniLibs/arm64-v8a/lib_burst_generated.so" }}
,{ "pid":12345, "tid":0, "ts":1752047081867817, "dur":71, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/artifacts/Android/il2cppOutput/build/arm64-v8a/libil2cpp.so" }}
,{ "pid":12345, "tid":0, "ts":1752047081867931, "dur":69, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"NdkObjCopy Library/Bee/artifacts/objcopy_5lmi/stripped/libil2cpp.so" }}
,{ "pid":12345, "tid":0, "ts":1752047081868006, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles Library/Bee/artifacts/objcopy_5lmi/libil2cpp.dbg.so" }}
,{ "pid":12345, "tid":0, "ts":1752047081868138, "dur":78, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/9kll8spbuk4q.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081868354, "dur":66, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/i54a6c2ohxdh.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081868453, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/jdprqsinlzuw.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081868554, "dur":68, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/p8v3lbbirbiz.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081868662, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/bwj8l05dy1ay.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081868750, "dur":69, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/xj7cmp094r3w.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081868825, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/iza11ye77ffg.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081868947, "dur":106, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/8ayg1b2oww8v.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081869069, "dur":87, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/tq14glgmzkkf.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081869168, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/irjo0akm51pw.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081869236, "dur":58, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/tleyyt55ari3.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081869302, "dur":65, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/1i84rizu2wfi.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081869374, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mybt7rds5kr1.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081869484, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/7dfhn1rp882k.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081869553, "dur":98, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/8lzx7g8ncbk0.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081869662, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/t12lnqmlfzvj.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081869731, "dur":65, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/u9m2752jj1cz.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081869803, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/fpqqnfhhmpoi.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081869874, "dur":65, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/04te3ovepd01.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081869947, "dur":95, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/1cevn4bdtfhh.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081870054, "dur":86, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mshj3dqaw3s0.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081870151, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/e1hme3j3dfum.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081870220, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/8k8n05ob51ed.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081870288, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/t0bcgf387pqw.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081870358, "dur":59, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/u8vs0uj7bs7b.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081870424, "dur":96, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/fozgg4x4egjv.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081870531, "dur":87, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/0324vdc2h4ue.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081870630, "dur":59, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/1bnlgts0l6bu.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081870697, "dur":59, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mrq9v26xound.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081870805, "dur":59, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/nzaqginwsw4s.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081870873, "dur":91, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/8eeewr1tvkgc.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081870975, "dur":87, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/tuh2b1fry8rv.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081871073, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/f912yi01hhb2.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081871143, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/fn868ses76d8.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081871214, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/gvsnt8uqb9un.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081871285, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/1awb9h9nex66.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081871356, "dur":94, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mqzzornlhliq.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081871462, "dur":55, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/0o5qesezk5ml.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081871525, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/1wp7y7uxo730.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081871594, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mctveh9urvfk.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081871651, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/7rwjuqnsujr3.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081871843, "dur":57, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/0j63ziscbd6i.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081871939, "dur":71, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/g85u144pif6u.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081872058, "dur":75, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2vtz1tzlp5yt.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081872180, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/gso6qzdxurfn.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081872271, "dur":66, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2fcbro8s1h8l.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081872375, "dur":55, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/8ajnm70n7tvo.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081872468, "dur":69, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/uy6rmwvjekon.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081872584, "dur":68, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/lz9rfs69d1h2.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081872713, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2el1jdpgu82y.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081872782, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/nuopzm3dxwei.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081872874, "dur":69, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/9hcuzby93m7g.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081873015, "dur":71, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/gl3mfa72d1bz.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081873124, "dur":79, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/28qrfz1ykr4x.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081873243, "dur":67, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/o1hwcr0ztpqa.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081873349, "dur":69, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/uwo879suz1dd.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081873458, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/gkcd8ynq6r6b.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081873571, "dur":58, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/27zh8nimdiza.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081873635, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/nn35nxwjg6au.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081873729, "dur":76, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/9araomrfnw3s.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081873878, "dur":67, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/gjl30n4dzi1o.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081873984, "dur":69, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/26870cz958tn.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081874059, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/3etolsf89ab3.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081874180, "dur":68, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/aikh1qo1jpfl.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081874286, "dur":71, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/gdrtw9gwp12n.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081874491, "dur":75, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/3d2eehwv215f.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081874638, "dur":67, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/aht7tf5ocgay.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081874711, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/vwwv9ojmf4lh.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081874835, "dur":74, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2znopnsfpiqz.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081874948, "dur":72, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/tal1f6v1rh3z.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081875059, "dur":69, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ag2xm4lc564a.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081875167, "dur":71, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/w3p2mtg8bxx9.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081875296, "dur":70, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2yweic92h9kc.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081875406, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/omkji13yozdb.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081875464, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/pu4z2gkxs1uq.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081875641, "dur":68, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hi2gvrbt7b45.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081875748, "dur":73, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/35plvg6oe2w4.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081875892, "dur":80, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/a9gebffiog1m.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081875978, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/bh1uvuvgsii2.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081876225, "dur":69, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/4cjs8k3abv8w.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081876333, "dur":72, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/a8p433v5g7vz.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081876509, "dur":84, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ij1d4hlxunhx.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081876633, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/46oi46ft1daw.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081876692, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/0dcuv44u1v7h.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081876786, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/bfjbh8srd07r.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081876902, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/x26fhxnnkq0q.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081877028, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/45x8wvwgu448.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081877098, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/pl1wc5aexsgs.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081877188, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/b9o1cu593i9q.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081877246, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/cpp1rpcsno71.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081877307, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/v8iwhyj1c5oc.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081877483, "dur":70, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/xe0ee8df9ia4.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081877559, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/db50b1bh2cpv.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081877722, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/vn7cdxf0399v.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081877781, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/01zek1xq1lza.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081877838, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/v0t4ioo2g7hl.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081877928, "dur":68, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/878swamkk5lx.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081878035, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/bz694xc5ywuw.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081878092, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/weayk7q21k6f.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081878181, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/i2x2kwly8aze.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081878238, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/3h1q05zvbzay.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081878295, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/4pl7klguf1rd.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081878384, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/alsjg48oldfg.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081878559, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ps28vfczluqh.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081878617, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/a85wbpqwoi20.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081878674, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/bgqdw46uskjg.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081878828, "dur":85, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/3ykurcul5mzh.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081879016, "dur":83, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/7sk29ap2btmg.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081879105, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/5hnporc7gi97.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081879248, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mrt9ubex4zyy.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081879306, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/8dae7wztktyl.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081879398, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/3rk5hkcdfkff.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081879487, "dur":67, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/rodfxnqmp95i.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081879560, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/yms2p2c88w6x.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081879618, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ghd97xez3wqy.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081879710, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/vrairmxpxc62.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081879767, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/1wgxn7sw6k2h.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081879857, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/nk41nwnsdbvg.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081879947, "dur":72, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/tfbejfgnjnij.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081880082, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/f3yij4ajqdbi.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081880175, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/83b95fuxpl22.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081880233, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ldqhcp8dvt5c.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081880405, "dur":86, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/v2ltsjojhjw6.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081880563, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/25cm8hxcrx0o.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081880662, "dur":84, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ot0q86r8xntn.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081880760, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/983eog650b56.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081880822, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/uo723pk33zgq.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081880932, "dur":55, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/tqqyoib0g7mb.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081880996, "dur":98, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mlw3ex27neg0.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081881106, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/70zru6g5q2rj.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081881175, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/88j8emx3u48y.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081881246, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/tonwuvb1xski.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081881326, "dur":381, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/uw7cfbsz1u1x.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081881788, "dur":80, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/1zy5u90sb95f.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081881876, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/a5qnuk1zwuff.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081881971, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/wzln42kclvsi.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081882062, "dur":58, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ig5tdiuec55o.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081882227, "dur":56, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2tyi0ld9hdjl.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081882323, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ohmn0a84o3bk.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081882380, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/9wpbgkm2rrn3.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081882471, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/m7tsc9fssqjw.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081882561, "dur":71, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/cqjhuhw737m8.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081882671, "dur":72, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/kdkmybhldeiu.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081882783, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/j46zdcerhtj0.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081882840, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mh7prgwf1kbz.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081882932, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/7sqf1sldz1ha.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081883128, "dur":89, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/x88dxcrw5he9.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081883258, "dur":67, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/uj5pvkt515uf.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081883367, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/o4pvni6nxhra.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081883490, "dur":72, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/egfpd4jb3rvv.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081883601, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/kbm18mc683iy.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081883659, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/lj6ht2s4c6zd.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081883774, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/m20qes3fkulp.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081883877, "dur":69, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/h4q9bzdazs6h.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081884038, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/d9rg6ujesit9.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081884182, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/kcj9lts71wxr.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081884273, "dur":67, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/6z6dmin38mqq.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081884414, "dur":67, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/d3x61gwwi1u8.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081884487, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/jbm9bjl27jgz.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081884634, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/qed2rhuvhykh.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081884727, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/w9jen0mqna8j.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081884817, "dur":68, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ix7inphmu00i.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081884924, "dur":68, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/xgnbosvtj371.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081885032, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/j4agohqpqt00.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081885091, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ykst251hxq25.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081885237, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/x76fzfo8l23p.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081885345, "dur":57, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/zulbjx00y6hz.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081885441, "dur":71, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/4qlh7yuo39bz.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081885518, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ezd2p1ujqlmu.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081885665, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/009ns7gybvdd.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081885840, "dur":70, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/yac4a4p3l7bk.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081885949, "dur":70, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/8535tzfadii5.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081886113, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/pwqgo80hc9gu.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081886361, "dur":72, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2qjvvj8f9f4h.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081886472, "dur":74, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/nyi813yiv8kp.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081886587, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/s2bwaz6dxjth.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081886644, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/uuardhox5g5u.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081886790, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/myajzagnq8e9.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081886879, "dur":65, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/sk3psbif85dy.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081887002, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/cc6dokwk1bcr.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081887216, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/w4ivjxtzymil.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081887306, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/6386mtmlure2.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081887396, "dur":72, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/19kcotfvceq7.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081887512, "dur":67, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/kvewo88f22kk.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081887671, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/leq6apo4lxfj.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081887917, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/jf3wjb0lbtlr.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081888022, "dur":69, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/vfm1o9u8rfhk.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081888097, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hf0tg52bd15c.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081888222, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ucxyqrog8tbt.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081888282, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/psc99vj4y3bm.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081888457, "dur":55, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/r2ko7fgfkwo7.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081888603, "dur":75, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mifvszs7cfcd.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081888684, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/uh35aip7mj9h.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081888828, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/sym525hss9ha.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081889003, "dur":69, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/m41fgba204hj.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081889112, "dur":65, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mw9ositkrg7u.o" }}
,{ "pid":12345, "tid":0, "ts":1752047081889216, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/12438370367218343051.rsp" }}
,{ "pid":12345, "tid":0, "ts":1752047081889274, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"NdkObjCopy Library/Bee/artifacts/objcopy_5lmi/libil2cpp.sym.so" }}
,{ "pid":12345, "tid":0, "ts":1752047081889419, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/artifacts/Android/il2cppOutput/build/arm64-v8a/Data/Metadata/global-metadata.dat" }}
,{ "pid":12345, "tid":0, "ts":1752047081889513, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/Managed/Metadata/global-metadata.dat" }}
,{ "pid":12345, "tid":0, "ts":1752047081889617, "dur":67, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/jniLibs/arm64-v8a/libil2cpp.so" }}
,{ "pid":12345, "tid":0, "ts":1752047081889724, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Assembly-CSharp-firstpass.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081889782, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Assembly-CSharp-firstpass_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1752047081889873, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Assembly-CSharp_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1752047081889931, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Assembly-CSharp__1.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081890041, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Assembly-CSharp__3.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081890139, "dur":74, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Assembly-CSharp__5.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081890252, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Assembly-CSharp__7.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081890309, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Assembly-CSharp__8.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081890451, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/DOTweenPro.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081890545, "dur":66, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/DOTween_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1752047081890684, "dur":68, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081890758, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__1.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081890902, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__12.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081891078, "dur":67, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__16.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081891183, "dur":69, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__18.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081891338, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__20.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081891430, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__22.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081891490, "dur":55, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__23.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081891602, "dur":59, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__25.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081891699, "dur":70, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__27.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081891774, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__28.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081891865, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__3.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081891922, "dur":55, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__30.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081892016, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__32.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081892106, "dur":68, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__4.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081892213, "dur":69, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__6.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081892391, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__9.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081892448, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081892573, "dur":55, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__100.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081892665, "dur":70, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__102.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081892774, "dur":70, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__12.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081892883, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__14.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081892941, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__15.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081893037, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__17.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081893094, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__18.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081893269, "dur":67, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__21.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081893342, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__22.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081893463, "dur":66, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__25.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081893567, "dur":59, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__27.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081893665, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__29.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081893722, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__3.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081893820, "dur":68, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__31.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081894016, "dur":77, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__35.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081894165, "dur":72, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__38.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081894243, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__39.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081894334, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__40.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081894423, "dur":70, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__42.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081894532, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__44.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081894622, "dur":70, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__46.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081894748, "dur":92, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__48.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081894852, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__49.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081894923, "dur":69, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__5.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081894999, "dur":66, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__50.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081895072, "dur":94, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__51.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081895177, "dur":87, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__52.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081895274, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__53.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081895345, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__54.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081895415, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__55.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081895483, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__56.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081895553, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__57.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081895623, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__58.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081895691, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__59.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081895760, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__6.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081895829, "dur":96, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__60.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081895936, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__61.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081896045, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__62.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081896117, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__63.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081896187, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__64.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081896257, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__65.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081896316, "dur":106, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__66.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081896442, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__67.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081896510, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__68.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081896580, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__69.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081896650, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__7.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081896721, "dur":94, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__70.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081896826, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__71.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081896897, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__72.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081896968, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__73.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081897037, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__74.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081897107, "dur":58, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__75.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081897205, "dur":58, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__76.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081897271, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__77.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081897342, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__78.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081897412, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__79.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081897483, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__8.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081897553, "dur":65, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__80.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081897625, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__81.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081897696, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__82.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081897766, "dur":66, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__83.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081897839, "dur":66, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__84.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081897912, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__85.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081897973, "dur":97, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__86.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081898081, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__87.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081898152, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__88.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081898220, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__89.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081898280, "dur":66, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__9.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081898384, "dur":65, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__90.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081898457, "dur":65, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__91.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081898535, "dur":89, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__92.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081898634, "dur":87, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__93.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081898732, "dur":89, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__94.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081898832, "dur":68, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__95.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081898907, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__96.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081898979, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__97.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081899048, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__98.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081899106, "dur":87, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__99.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081899204, "dur":87, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppCCalculateFieldValues.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081899302, "dur":87, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppCCalculateFieldValues1.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081899400, "dur":71, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppCCalculateFieldValues2.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081899479, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppCCalculateTypeValues.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081899538, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppCCalculateTypeValues1.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081899655, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppCCTypeValuesTable.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081899756, "dur":69, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppGenericAdjustorThunkTable.c" }}
,{ "pid":12345, "tid":0, "ts":1752047081899899, "dur":84, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppGenericMethodDefinitions.c" }}
,{ "pid":12345, "tid":0, "ts":1752047081900023, "dur":69, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppGenericMethodTable.c" }}
,{ "pid":12345, "tid":0, "ts":1752047081900134, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppInvokerTable.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081900293, "dur":65, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppRgctxTable.c" }}
,{ "pid":12345, "tid":0, "ts":1752047081900416, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppTypeDefinitions.c" }}
,{ "pid":12345, "tid":0, "ts":1752047081900519, "dur":70, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Mono.Security.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081900628, "dur":71, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/mscorlib.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081900790, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/mscorlib__10.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081900964, "dur":68, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/mscorlib__14.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081901070, "dur":69, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/mscorlib__16.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081901163, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/mscorlib__17.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081901224, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/mscorlib__18.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081901314, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/mscorlib__3.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081901371, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/mscorlib__4.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081901460, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/mscorlib__6.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081901598, "dur":69, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/mscorlib__9.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081901706, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/System.Configuration_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1752047081901837, "dur":56, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/System.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081902089, "dur":74, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/System__1.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081902236, "dur":66, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.Burst.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081902374, "dur":88, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.Burst_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1752047081902501, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.Cinemachine_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1752047081902597, "dur":56, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.Collections.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081902764, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.InputSystem.ForUI.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081902865, "dur":65, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.InputSystem_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1752047081903002, "dur":67, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.InputSystem__11.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081903075, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.InputSystem__2.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081903200, "dur":58, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.InputSystem__5.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081903379, "dur":69, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.InputSystem__9.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081903505, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.Mathematics_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1752047081903607, "dur":57, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.Postprocessing.Runtime_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1752047081903670, "dur":59, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.Postprocessing.Runtime__1.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081903802, "dur":59, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.RenderPipelines.Core.Runtime_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1752047081903932, "dur":57, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.RenderPipelines.Core.Runtime__3.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081904028, "dur":67, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.RenderPipelines.Core.Runtime__5.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081904133, "dur":70, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.RenderPipelines.Core.Runtime__7.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081904260, "dur":71, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.TextMeshPro.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081904371, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.TextMeshPro__1.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081904433, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.TextMeshPro__2.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081904492, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.TextMeshPro__3.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081904582, "dur":67, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.TextMeshPro__5.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081904722, "dur":72, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.TextMeshPro__8.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081904832, "dur":68, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.AccessibilityModule_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1752047081904906, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.AIModule.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081905014, "dur":55, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.AndroidJNIModule.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081905109, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.AnimationModule.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081905200, "dur":66, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.AssetBundleModule.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081905305, "dur":67, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.AudioModule.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081905444, "dur":75, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.CoreModule_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1752047081905525, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.CoreModule__1.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081905650, "dur":56, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.CoreModule__4.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081905712, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081905829, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.DirectorModule_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1752047081905928, "dur":68, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.GridModule_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1752047081906036, "dur":68, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.HierarchyCoreModule_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1752047081906110, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.IMGUIModule.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081906201, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.IMGUIModule__1.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081906258, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.InputForUIModule.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081906382, "dur":67, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.InputLegacyModule_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1752047081906487, "dur":69, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.InputModule_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1752047081906615, "dur":72, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.JSONSerializeModule_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1752047081906780, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.Physics2DModule.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081906959, "dur":67, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.PropertiesModule.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081907069, "dur":67, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.SharedInternalsModule.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081907174, "dur":69, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.SpriteShapeModule.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081907367, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.TerrainModule.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081907466, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.TerrainPhysicsModule.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081907557, "dur":72, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.TextCoreFontEngineModule.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081907668, "dur":68, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.TextCoreTextEngineModule.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081907807, "dur":70, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.TextCoreTextEngineModule__2.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081907915, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.TextCoreTextEngineModule__4.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081907989, "dur":56, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.TextRenderingModule.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081908086, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.TilemapModule.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081908176, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UI.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081908301, "dur":89, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__1.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081908462, "dur":85, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__12.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081908588, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__14.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081908738, "dur":58, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__17.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081908803, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__18.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081908894, "dur":65, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__2.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081909029, "dur":65, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__5.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081909135, "dur":58, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__7.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081909199, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__8.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081909288, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIModule.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081909346, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIModule_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1752047081909436, "dur":66, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UI__1.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081909562, "dur":69, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UI__3.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081909724, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UnityAnalyticsModule.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081909785, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UnityAnalyticsModule_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1752047081909878, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UnityWebRequestModule_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1752047081909969, "dur":73, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.VehiclesModule_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1752047081910081, "dur":68, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.VFXModule_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1752047081910190, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.VideoModule_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1752047081910320, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.VRModule_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1752047081910411, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.XRModule_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1752047081910501, "dur":74, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/__Generated.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752047081910617, "dur":76, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/analytics.json" }}
,{ "pid":12345, "tid":0, "ts":1752047081910732, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Assembly-CSharp-firstpass.pdb" }}
,{ "pid":12345, "tid":0, "ts":1752047081910791, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":0, "ts":1752047081910882, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/DOTween.dll" }}
,{ "pid":12345, "tid":0, "ts":1752047081910940, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/DOTweenPro.dll" }}
,{ "pid":12345, "tid":0, "ts":1752047081911029, "dur":66, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/mscorlib.dll" }}
,{ "pid":12345, "tid":0, "ts":1752047081911157, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/System.Core.dll" }}
,{ "pid":12345, "tid":0, "ts":1752047081911326, "dur":88, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.Burst.pdb" }}
,{ "pid":12345, "tid":0, "ts":1752047081911420, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.Burst.Unsafe.dll" }}
,{ "pid":12345, "tid":0, "ts":1752047081911543, "dur":55, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.Collections.dll" }}
,{ "pid":12345, "tid":0, "ts":1752047081911604, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.Collections.pdb" }}
,{ "pid":12345, "tid":0, "ts":1752047081911695, "dur":73, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.InputSystem.ForUI.dll" }}
,{ "pid":12345, "tid":0, "ts":1752047081911913, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.Mathematics.pdb" }}
,{ "pid":12345, "tid":0, "ts":1752047081912005, "dur":66, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.Postprocessing.Runtime.pdb" }}
,{ "pid":12345, "tid":0, "ts":1752047081912143, "dur":66, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":0, "ts":1752047081912247, "dur":77, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.AccessibilityModule.dll" }}
,{ "pid":12345, "tid":0, "ts":1752047081912362, "dur":69, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.AIModule.dll" }}
,{ "pid":12345, "tid":0, "ts":1752047081912509, "dur":67, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.AndroidJNIModule.pdb" }}
,{ "pid":12345, "tid":0, "ts":1752047081912643, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.AnimationModule.pdb" }}
,{ "pid":12345, "tid":0, "ts":1752047081912745, "dur":85, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.AssetBundleModule.pdb" }}
,{ "pid":12345, "tid":0, "ts":1752047081912869, "dur":55, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.AudioModule.pdb" }}
,{ "pid":12345, "tid":0, "ts":1752047081912929, "dur":55, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.CoreModule.dll" }}
,{ "pid":12345, "tid":0, "ts":1752047081913023, "dur":70, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.DirectorModule.dll" }}
,{ "pid":12345, "tid":0, "ts":1752047081913099, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.DirectorModule.pdb" }}
,{ "pid":12345, "tid":0, "ts":1752047081913241, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.GridModule.pdb" }}
,{ "pid":12345, "tid":0, "ts":1752047081913299, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.HierarchyCoreModule.dll" }}
,{ "pid":12345, "tid":0, "ts":1752047081913711, "dur":59, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.InputModule.pdb" }}
,{ "pid":12345, "tid":0, "ts":1752047081913776, "dur":71, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.JSONSerializeModule.dll" }}
,{ "pid":12345, "tid":0, "ts":1752047081913891, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.ParticleSystemModule.dll" }}
,{ "pid":12345, "tid":0, "ts":1752047081913957, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.ParticleSystemModule.pdb" }}
,{ "pid":12345, "tid":0, "ts":1752047081914016, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.pdb" }}
,{ "pid":12345, "tid":0, "ts":1752047081914347, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.PropertiesModule.dll" }}
,{ "pid":12345, "tid":0, "ts":1752047081914563, "dur":67, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.SpriteShapeModule.pdb" }}
,{ "pid":12345, "tid":0, "ts":1752047081914636, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.SubsystemsModule.dll" }}
,{ "pid":12345, "tid":0, "ts":1752047081914752, "dur":66, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TerrainModule.pdb" }}
,{ "pid":12345, "tid":0, "ts":1752047081914824, "dur":104, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TerrainPhysicsModule.dll" }}
,{ "pid":12345, "tid":0, "ts":1752047081914947, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TerrainPhysicsModule.pdb" }}
,{ "pid":12345, "tid":0, "ts":1752047081915008, "dur":116, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TextCoreFontEngineModule.dll" }}
,{ "pid":12345, "tid":0, "ts":1752047081915145, "dur":78, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TextCoreFontEngineModule.pdb" }}
,{ "pid":12345, "tid":0, "ts":1752047081915269, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TextCoreTextEngineModule.dll" }}
,{ "pid":12345, "tid":0, "ts":1752047081915340, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TextCoreTextEngineModule.pdb" }}
,{ "pid":12345, "tid":0, "ts":1752047081915411, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TextRenderingModule.dll" }}
,{ "pid":12345, "tid":0, "ts":1752047081915481, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TextRenderingModule.pdb" }}
,{ "pid":12345, "tid":0, "ts":1752047081915552, "dur":57, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TilemapModule.dll" }}
,{ "pid":12345, "tid":0, "ts":1752047081915626, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TilemapModule.pdb" }}
,{ "pid":12345, "tid":0, "ts":1752047081915687, "dur":70, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":0, "ts":1752047081915765, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.UI.pdb" }}
,{ "pid":12345, "tid":0, "ts":1752047081915834, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.UIElementsModule.dll" }}
,{ "pid":12345, "tid":0, "ts":1752047081915903, "dur":67, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.UIElementsModule.pdb" }}
,{ "pid":12345, "tid":0, "ts":1752047081915977, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.UIModule.dll" }}
,{ "pid":12345, "tid":0, "ts":1752047081916047, "dur":58, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.UIModule.pdb" }}
,{ "pid":12345, "tid":0, "ts":1752047081916113, "dur":100, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.UnityAnalyticsCommonModule.dll" }}
,{ "pid":12345, "tid":0, "ts":1752047081916225, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.UnityAnalyticsCommonModule.pdb" }}
,{ "pid":12345, "tid":0, "ts":1752047081916287, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.UnityAnalyticsModule.dll" }}
,{ "pid":12345, "tid":0, "ts":1752047081916596, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.VFXModule.dll" }}
,{ "pid":12345, "tid":0, "ts":1752047081916835, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.VRModule.pdb" }}
,{ "pid":12345, "tid":0, "ts":1752047081917525, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values-v28/styles.xml" }}
,{ "pid":12345, "tid":0, "ts":1752047081917611, "dur":76, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/styles.xml" }}
,{ "pid":12345, "tid":0, "ts":1752047081917767, "dur":71, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/strings.xml" }}
,{ "pid":12345, "tid":0, "ts":1752047081917844, "dur":56, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values-v30/freeformwindow.xml" }}
,{ "pid":12345, "tid":0, "ts":1752047081918012, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-anydpi-v26/app_icon_round.xml" }}
,{ "pid":12345, "tid":0, "ts":1752047081918200, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/jniLibs/arm64-v8a/libmain.so" }}
,{ "pid":12345, "tid":0, "ts":1752047081918286, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"GuidGenerator D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/unity_app_guid" }}
,{ "pid":12345, "tid":0, "ts":1752047081860416, "dur":58087, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752047081918516, "dur":13303328, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752047095221851, "dur":1139, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752047095222990, "dur":199, "ph":"X", "name": "ThreadStateDestroy",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752047095223237, "dur":64, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752047095223366, "dur":84, "ph":"X", "name": "ThreadStateDestroy",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752047095223549, "dur":56, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752047095223694, "dur":68, "ph":"X", "name": "BuildQueueDestroyTail",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752047095223803, "dur":13820, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1752047081859674, "dur":58855, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752047081924005, "dur":1222, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/build/deploy" }}
,{ "pid":12345, "tid":1, "ts":1752047081925227, "dur":129, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/external/xamarin-android" }}
,{ "pid":12345, "tid":1, "ts":1752047081925357, "dur":127, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/external/zlib" }}
,{ "pid":12345, "tid":1, "ts":1752047081925485, "dur":135, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/external/zlib" }}
,{ "pid":12345, "tid":1, "ts":1752047081925620, "dur":123, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/external/zlib" }}
,{ "pid":12345, "tid":1, "ts":1752047081925743, "dur":111, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/external/zlib" }}
,{ "pid":12345, "tid":1, "ts":1752047081925854, "dur":127, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/external/zlib" }}
,{ "pid":12345, "tid":1, "ts":1752047081925982, "dur":109, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/external/zlib" }}
,{ "pid":12345, "tid":1, "ts":1752047081926091, "dur":161, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/external/zlib" }}
,{ "pid":12345, "tid":1, "ts":1752047081926252, "dur":8379, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp" }}
,{ "pid":12345, "tid":1, "ts":1752047081934631, "dur":100, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli" }}
,{ "pid":12345, "tid":1, "ts":1752047081934731, "dur":87, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli" }}
,{ "pid":12345, "tid":1, "ts":1752047081934818, "dur":85, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli" }}
,{ "pid":12345, "tid":1, "ts":1752047081934903, "dur":97, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli" }}
,{ "pid":12345, "tid":1, "ts":1752047081935001, "dur":113, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli" }}
,{ "pid":12345, "tid":1, "ts":1752047081935114, "dur":116, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/common" }}
,{ "pid":12345, "tid":1, "ts":1752047081935230, "dur":147, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/common" }}
,{ "pid":12345, "tid":1, "ts":1752047081935377, "dur":96, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/common" }}
,{ "pid":12345, "tid":1, "ts":1752047081935473, "dur":96, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/common" }}
,{ "pid":12345, "tid":1, "ts":1752047081935569, "dur":106, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/common" }}
,{ "pid":12345, "tid":1, "ts":1752047081935675, "dur":96, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/dec" }}
,{ "pid":12345, "tid":1, "ts":1752047081935771, "dur":98, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/dec" }}
,{ "pid":12345, "tid":1, "ts":1752047081935869, "dur":105, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/dec" }}
,{ "pid":12345, "tid":1, "ts":1752047081935974, "dur":100, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/dec" }}
,{ "pid":12345, "tid":1, "ts":1752047081936074, "dur":106, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/dec" }}
,{ "pid":12345, "tid":1, "ts":1752047081936180, "dur":204, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/enc" }}
,{ "pid":12345, "tid":1, "ts":1752047081936384, "dur":209, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/enc" }}
,{ "pid":12345, "tid":1, "ts":1752047081936594, "dur":181, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/enc" }}
,{ "pid":12345, "tid":1, "ts":1752047081936775, "dur":209, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/enc" }}
,{ "pid":12345, "tid":1, "ts":1752047081936984, "dur":328, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/enc" }}
,{ "pid":12345, "tid":1, "ts":1752047081937312, "dur":92, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" }}
,{ "pid":12345, "tid":1, "ts":1752047081937404, "dur":87, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" }}
,{ "pid":12345, "tid":1, "ts":1752047081937491, "dur":85, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" }}
,{ "pid":12345, "tid":1, "ts":1752047081937576, "dur":85, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" }}
,{ "pid":12345, "tid":1, "ts":1752047081937665, "dur":89, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" }}
,{ "pid":12345, "tid":1, "ts":1752047081937754, "dur":90, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include/brotli" }}
,{ "pid":12345, "tid":1, "ts":1752047081937844, "dur":100, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include/brotli" }}
,{ "pid":12345, "tid":1, "ts":1752047081937945, "dur":91, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include/brotli" }}
,{ "pid":12345, "tid":1, "ts":1752047081938036, "dur":97, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include/brotli" }}
,{ "pid":12345, "tid":1, "ts":1752047081938133, "dur":119, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include/brotli" }}
,{ "pid":12345, "tid":1, "ts":1752047081938253, "dur":134, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer" }}
,{ "pid":12345, "tid":1, "ts":1752047081938387, "dur":903, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Apk" }}
,{ "pid":12345, "tid":1, "ts":1752047081939290, "dur":917, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Apk" }}
,{ "pid":12345, "tid":1, "ts":1752047081940208, "dur":977, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Apk" }}
,{ "pid":12345, "tid":1, "ts":1752047081941185, "dur":107, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Apk/res/mipmap-anydpi-v26" }}
,{ "pid":12345, "tid":1, "ts":1752047081941292, "dur":110, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Apk/res/mipmap-mdpi" }}
,{ "pid":12345, "tid":1, "ts":1752047081941402, "dur":186, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity" }}
,{ "pid":12345, "tid":1, "ts":1752047081941589, "dur":111, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Release/Libs/arm64-v8a" }}
,{ "pid":12345, "tid":1, "ts":1752047081941700, "dur":370, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":1, "ts":1752047081942070, "dur":304, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":1, "ts":1752047081942374, "dur":120, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"Library/PlayerDataCache/Android/Data" }}
,{ "pid":12345, "tid":1, "ts":1752047081942495, "dur":66, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"Temp/StagingArea/Data/UnitySubsystems" }}
,{ "pid":12345, "tid":1, "ts":1752047081918533, "dur":24134, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752047081942688, "dur":204, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/15538298867967171994.rsp" }}
,{ "pid":12345, "tid":1, "ts":1752047081944361, "dur":468, "ph":"X", "name": "WriteResponseFile",  "args": { "detail":"Library/Bee/artifacts/rsp/15538298867967171994.rsp" }}
,{ "pid":12345, "tid":1, "ts":1752047081945013, "dur":682, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":1, "ts":1752047081945699, "dur":508, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Cinemachine.dll" }}
,{ "pid":12345, "tid":1, "ts":1752047081946210, "dur":606, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Collections.dll" }}
,{ "pid":12345, "tid":1, "ts":1752047081946819, "dur":1710, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\Unity.InputSystem.dll" }}
,{ "pid":12345, "tid":1, "ts":1752047081948532, "dur":152, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\Unity.InputSystem.ForUI.dll" }}
,{ "pid":12345, "tid":1, "ts":1752047081948692, "dur":1349, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":1, "ts":1752047081950046, "dur":127, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Multiplayer.Center.Common.dll" }}
,{ "pid":12345, "tid":1, "ts":1752047081950176, "dur":361, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Postprocessing.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1752047081950540, "dur":570, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Rendering.LightTransport.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1752047081951118, "dur":1445, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1752047081952567, "dur":155, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.Shared.dll" }}
,{ "pid":12345, "tid":1, "ts":1752047081952732, "dur":324, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.ShaderLibrary.dll" }}
,{ "pid":12345, "tid":1, "ts":1752047081953076, "dur":585, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.GPUDriven.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1752047081953669, "dur":219, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll" }}
,{ "pid":12345, "tid":1, "ts":1752047081953899, "dur":342, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Splines.dll" }}
,{ "pid":12345, "tid":1, "ts":1752047081954246, "dur":819, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":1, "ts":1752047081955068, "dur":284, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Timeline.dll" }}
,{ "pid":12345, "tid":1, "ts":1752047081955362, "dur":992, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Core.dll" }}
,{ "pid":12345, "tid":1, "ts":1752047081956366, "dur":756, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Flow.dll" }}
,{ "pid":12345, "tid":1, "ts":1752047081957134, "dur":186, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.State.dll" }}
,{ "pid":12345, "tid":1, "ts":1752047081957324, "dur":419, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":1, "ts":1752047081958585, "dur":124, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\UnityLinkerInputs\\MethodsToPreserve.xml" }}
,{ "pid":12345, "tid":1, "ts":1752047081958712, "dur":112, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\UnityLinkerInputs\\TypesInScenes.xml" }}
,{ "pid":12345, "tid":1, "ts":1752047081958828, "dur":94, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\UnityLinkerInputs\\SerializedTypes.xml" }}
,{ "pid":12345, "tid":1, "ts":1752047081958925, "dur":110, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\InputSystem\\AndroidLink.xml" }}
,{ "pid":12345, "tid":1, "ts":1752047081959038, "dur":223, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\UnityLinkerInputs\\EditorToUnityLinkerData.json" }}
,{ "pid":12345, "tid":1, "ts":1752047081959948, "dur":13468, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\rsp\\15538298867967171994.rsp" }}
,{ "pid":12345, "tid":1, "ts":1752047081944831, "dur":28589, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"UnityLinker D:/My Project/Driving Simulator Game Z TEC/Library/Bee/artifacts/unitylinker_xy1a.traceevents" }}
,{ "pid":12345, "tid":1, "ts":1752047081975062, "dur":555, "ph":"X", "name": "StoreTimestampsOfNonGeneratedInputFiles",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752047082091976, "dur":13106929, "ph":"X", "name": "UnityLinker",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/artifacts/unitylinker_xy1a.traceevents" }}
,{ "pid":12345, "tid":2, "ts":1752047081859922, "dur":58672, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752047081918600, "dur":2557, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752047081921158, "dur":1432, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752047081922590, "dur":20105, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752047081942699, "dur":59, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.Mathematics-FeaturesChecked.txt_bwkn.info" }}
,{ "pid":12345, "tid":2, "ts":1752047081942761, "dur":115, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752047081942877, "dur":364, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.Mathematics-FeaturesChecked.txt_bwkn.info" }}
,{ "pid":12345, "tid":2, "ts":1752047081943243, "dur":130, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/jniLibs/arm64-v8a/libunity.so" }}
,{ "pid":12345, "tid":2, "ts":1752047081943374, "dur":75, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/jniLibs/arm64-v8a/libunity.so" }}
,{ "pid":12345, "tid":2, "ts":1752047081943478, "dur":229, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/ids.xml" }}
,{ "pid":12345, "tid":2, "ts":1752047081943708, "dur":61, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/ids.xml" }}
,{ "pid":12345, "tid":2, "ts":1752047081943785, "dur":13278419, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752047081860970, "dur":57796, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752047081918771, "dur":2600, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752047081921372, "dur":2246, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752047081923619, "dur":19097, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752047081942746, "dur":56, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752047081943138, "dur":618, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.AssetBundleModule-FeaturesChecked.txt_1gjh.info" }}
,{ "pid":12345, "tid":3, "ts":1752047081943757, "dur":13278261, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752047081859910, "dur":58664, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752047081921065, "dur":558, "ph":"X", "name": "File",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Source\\GameActivity\\ReadMe.txt" }}
,{ "pid":12345, "tid":4, "ts":1752047081918579, "dur":3540, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752047081922119, "dur":20643, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752047081942769, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752047081942881, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752047081942937, "dur":52, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UnityAnalyticsModule-FeaturesChecked.txt_cjxw.info" }}
,{ "pid":12345, "tid":4, "ts":1752047081943052, "dur":725, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.Physics2DModule-FeaturesChecked.txt_45hf.info" }}
,{ "pid":12345, "tid":4, "ts":1752047081943778, "dur":13278227, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752047081859980, "dur":58628, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752047081918612, "dur":2561, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752047081921174, "dur":1525, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752047081922700, "dur":19978, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752047081942690, "dur":149015, "ph":"X", "name": "ComputeLeafInputSignature",  "args": { "detail":"Lib_Android_arm64 Library/Bee/artifacts/Android/87lik/il2cpp.a" }}
,{ "pid":12345, "tid":5, "ts":1752047082091707, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752047082091759, "dur":13130381, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752047081860072, "dur":58554, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752047081918633, "dur":3026, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752047081921659, "dur":21075, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752047081942736, "dur":56, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/System-FeaturesChecked.txt_v90b.info" }}
,{ "pid":12345, "tid":6, "ts":1752047081942794, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752047081942863, "dur":142, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/System-FeaturesChecked.txt_v90b.info" }}
,{ "pid":12345, "tid":6, "ts":1752047081943189, "dur":71, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/libs/unity-classes.jar" }}
,{ "pid":12345, "tid":6, "ts":1752047081943302, "dur":208, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-mdpi/app_icon.png" }}
,{ "pid":12345, "tid":6, "ts":1752047081943511, "dur":118, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-mdpi/app_icon.png" }}
,{ "pid":12345, "tid":6, "ts":1752047081943646, "dur":13278572, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752047081860158, "dur":58481, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752047081918643, "dur":3074, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752047081921718, "dur":20974, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752047081942697, "dur":57, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.Postprocessing.Runtime-FeaturesChecked.txt_6gzn.info" }}
,{ "pid":12345, "tid":7, "ts":1752047081942756, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752047081942867, "dur":99, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752047081942966, "dur":669, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UnityWebRequestModule-FeaturesChecked.txt_jk15.info" }}
,{ "pid":12345, "tid":7, "ts":1752047081943638, "dur":13278251, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752047081860249, "dur":58400, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752047081918654, "dur":2896, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752047081921551, "dur":21173, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752047081942943, "dur":642, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UIElementsModule-FeaturesChecked.txt_aabo.info" }}
,{ "pid":12345, "tid":8, "ts":1752047081943587, "dur":13278540, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752047081860303, "dur":58359, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752047081918666, "dur":2578, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752047081921245, "dur":1131, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752047081922377, "dur":20323, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752047081942720, "dur":107, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.InputSystem.ForUI-FeaturesChecked.txt_1qsl.info" }}
,{ "pid":12345, "tid":9, "ts":1752047081942870, "dur":247, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.InputSystem.ForUI-FeaturesChecked.txt_1qsl.info" }}
,{ "pid":12345, "tid":9, "ts":1752047081943185, "dur":66, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/boot.config_x4e9.info" }}
,{ "pid":12345, "tid":9, "ts":1752047081943347, "dur":283, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PlayerDataCache\\Android\\Data\\boot.config" }}
,{ "pid":12345, "tid":9, "ts":1752047081943639, "dur":155, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PlayerDataCache\\Android\\Data\\ScriptingAssemblies.json" }}
,{ "pid":12345, "tid":9, "ts":1752047081944096, "dur":168, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\Assembly-CSharp-firstpass.dll" }}
,{ "pid":12345, "tid":9, "ts":1752047081944272, "dur":806, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":9, "ts":1752047081945085, "dur":406, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Burst.dll" }}
,{ "pid":12345, "tid":9, "ts":1752047081945501, "dur":657, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Cinemachine.dll" }}
,{ "pid":12345, "tid":9, "ts":1752047081946161, "dur":605, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Collections.dll" }}
,{ "pid":12345, "tid":9, "ts":1752047081946773, "dur":1737, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\Unity.InputSystem.dll" }}
,{ "pid":12345, "tid":9, "ts":1752047081948518, "dur":194, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\Unity.InputSystem.ForUI.dll" }}
,{ "pid":12345, "tid":9, "ts":1752047081948715, "dur":1365, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":9, "ts":1752047081950083, "dur":126, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Multiplayer.Center.Common.dll" }}
,{ "pid":12345, "tid":9, "ts":1752047081950212, "dur":312, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Postprocessing.Runtime.dll" }}
,{ "pid":12345, "tid":9, "ts":1752047081950530, "dur":590, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Rendering.LightTransport.Runtime.dll" }}
,{ "pid":12345, "tid":9, "ts":1752047081951123, "dur":1424, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.dll" }}
,{ "pid":12345, "tid":9, "ts":1752047081952559, "dur":179, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.Shared.dll" }}
,{ "pid":12345, "tid":9, "ts":1752047081952741, "dur":365, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.ShaderLibrary.dll" }}
,{ "pid":12345, "tid":9, "ts":1752047081953111, "dur":552, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.GPUDriven.Runtime.dll" }}
,{ "pid":12345, "tid":9, "ts":1752047081953669, "dur":187, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll" }}
,{ "pid":12345, "tid":9, "ts":1752047081953884, "dur":341, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Splines.dll" }}
,{ "pid":12345, "tid":9, "ts":1752047081954229, "dur":812, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":9, "ts":1752047081955054, "dur":320, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Timeline.dll" }}
,{ "pid":12345, "tid":9, "ts":1752047081955377, "dur":996, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Core.dll" }}
,{ "pid":12345, "tid":9, "ts":1752047081956376, "dur":760, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Flow.dll" }}
,{ "pid":12345, "tid":9, "ts":1752047081957141, "dur":218, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.State.dll" }}
,{ "pid":12345, "tid":9, "ts":1752047081957363, "dur":450, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":9, "ts":1752047081958622, "dur":227, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\Player4d237629-inputdata.json" }}
,{ "pid":12345, "tid":9, "ts":1752047081943292, "dur":15641, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"AddBootConfigGUID Library/Bee/artifacts/Android/boot.config" }}
,{ "pid":12345, "tid":9, "ts":1752047081960062, "dur":237, "ph":"X", "name": "StoreTimestampsOfNonGeneratedInputFiles",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752047081960718, "dur":441562, "ph":"X", "name": "AddBootConfigGUID",  "args": { "detail":"Library/Bee/artifacts/Android/boot.config" }}
,{ "pid":12345, "tid":9, "ts":1752047082415895, "dur":274, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\Android\\boot.config" }}
,{ "pid":12345, "tid":9, "ts":1752047082415876, "dur":295, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/boot.config" }}
,{ "pid":12345, "tid":9, "ts":1752047082417205, "dur":9905, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\assets\\bin\\Data\\boot.config" }}
,{ "pid":12345, "tid":9, "ts":1752047082417185, "dur":9927, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/boot.config" }}
,{ "pid":12345, "tid":9, "ts":1752047082416281, "dur":10868, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/boot.config" }}
,{ "pid":12345, "tid":9, "ts":1752047082427152, "dur":12794805, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752047081860364, "dur":58314, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752047081918683, "dur":2513, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752047081921197, "dur":1377, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752047081922575, "dur":20163, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752047081942785, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752047081942901, "dur":59, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752047081942961, "dur":388, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UIModule-FeaturesChecked.txt_zatd.info" }}
,{ "pid":12345, "tid":10, "ts":1752047081943351, "dur":125, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values-v30/freeformwindow.xml" }}
,{ "pid":12345, "tid":10, "ts":1752047081943545, "dur":178, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PlayerDataCache\\Android\\Data\\ScriptingAssemblies.json" }}
,{ "pid":12345, "tid":10, "ts":1752047081943536, "dur":188, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/ScriptingAssemblies.json" }}
,{ "pid":12345, "tid":10, "ts":1752047081943765, "dur":13278392, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752047081860426, "dur":58266, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752047081918697, "dur":2972, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752047081921669, "dur":21021, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752047081942735, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752047081942873, "dur":75, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Assembly-CSharp-firstpass-FeaturesChecked.txt_ww6f.info" }}
,{ "pid":12345, "tid":11, "ts":1752047081942978, "dur":351, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.TerrainPhysicsModule-FeaturesChecked.txt_15vy.info" }}
,{ "pid":12345, "tid":11, "ts":1752047081943331, "dur":144, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-anydpi-v26/app_icon.xml" }}
,{ "pid":12345, "tid":11, "ts":1752047081943475, "dur":66, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-anydpi-v26/app_icon.xml" }}
,{ "pid":12345, "tid":11, "ts":1752047081943543, "dur":112, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-anydpi-v26/app_icon.xml" }}
,{ "pid":12345, "tid":11, "ts":1752047081943657, "dur":13278430, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752047081860513, "dur":58193, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752047081918711, "dur":2653, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752047081921365, "dur":602, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752047081921968, "dur":20740, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752047081942958, "dur":317, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.TilemapModule-FeaturesChecked.txt_xct4.info" }}
,{ "pid":12345, "tid":12, "ts":1752047081943277, "dur":104, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-mdpi/ic_launcher_background.png" }}
,{ "pid":12345, "tid":12, "ts":1752047081943412, "dur":398, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-mdpi/ic_launcher_background.png" }}
,{ "pid":12345, "tid":12, "ts":1752047081943813, "dur":13278433, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752047081860636, "dur":58086, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752047081918728, "dur":2503, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752047081921232, "dur":2391, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752047081923625, "dur":19058, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752047081942690, "dur":112, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.AIModule-FeaturesChecked.txt_jr17.info" }}
,{ "pid":12345, "tid":13, "ts":1752047081942803, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752047081942873, "dur":863, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.AIModule-FeaturesChecked.txt_jr17.info" }}
,{ "pid":12345, "tid":13, "ts":1752047081943738, "dur":13278305, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752047081860769, "dur":57969, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752047081918743, "dur":3072, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752047081921816, "dur":20871, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752047081942816, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752047081942869, "dur":216, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Assembly-CSharp-FeaturesChecked.txt_ki5r.info" }}
,{ "pid":12345, "tid":14, "ts":1752047081943127, "dur":394, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.IMGUIModule-FeaturesChecked.txt_ufk2.info" }}
,{ "pid":12345, "tid":14, "ts":1752047081943574, "dur":289, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/Resources/unity default resources" }}
,{ "pid":12345, "tid":14, "ts":1752047081943864, "dur":13278015, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752047081860873, "dur":57880, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752047081918759, "dur":2739, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752047081921499, "dur":801, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752047081922301, "dur":20413, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752047081942716, "dur":122, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.SharedInternalsModule-FeaturesChecked.txt_sm9d.info" }}
,{ "pid":12345, "tid":15, "ts":1752047081942840, "dur":74, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752047081942941, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752047081942996, "dur":406, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.TextCoreTextEngineModule-FeaturesChecked.txt_el85.info" }}
,{ "pid":12345, "tid":15, "ts":1752047081943409, "dur":122, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"WriteText D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/styles.xml" }}
,{ "pid":12345, "tid":15, "ts":1752047081943592, "dur":70, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/resources.resource" }}
,{ "pid":12345, "tid":15, "ts":1752047081964482, "dur":15935, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/resources.resource" }}
,{ "pid":12345, "tid":15, "ts":1752047081980465, "dur":13241478, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752047081859762, "dur":58786, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752047081918567, "dur":2935, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752047081921503, "dur":21177, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752047081942770, "dur":916, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.AndroidJNIModule-FeaturesChecked.txt_x3ln.info" }}
,{ "pid":12345, "tid":16, "ts":1752047081943689, "dur":13278302, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752047081861089, "dur":57691, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752047081918784, "dur":1915, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752047081922601, "dur":554, "ph":"X", "name": "File",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityaot-linux\\Facades\\System.Runtime.Serialization.Xml.dll" }}
,{ "pid":12345, "tid":17, "ts":1752047081920699, "dur":2652, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752047081923352, "dur":19434, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752047081942809, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752047081942886, "dur":115, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752047081943111, "dur":377, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.InputForUIModule-FeaturesChecked.txt_awp5.info" }}
,{ "pid":12345, "tid":17, "ts":1752047081943490, "dur":130, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/strings.xml" }}
,{ "pid":12345, "tid":17, "ts":1752047081943651, "dur":194, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/strings.xml" }}
,{ "pid":12345, "tid":17, "ts":1752047081943847, "dur":13278386, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752047081861174, "dur":57620, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752047081918798, "dur":3065, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752047081921864, "dur":20812, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752047081942696, "dur":128, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/12438370367218343051.rsp" }}
,{ "pid":12345, "tid":18, "ts":1752047081942826, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752047081942890, "dur":132, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/12438370367218343051.rsp" }}
,{ "pid":12345, "tid":18, "ts":1752047081943117, "dur":345, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine-FeaturesChecked.txt_x1ug.info" }}
,{ "pid":12345, "tid":18, "ts":1752047081943465, "dur":86, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"WriteText D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values-v21/styles.xml" }}
,{ "pid":12345, "tid":18, "ts":1752047081945199, "dur":108751, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/data.unity3d" }}
,{ "pid":12345, "tid":18, "ts":1752047082053973, "dur":13168101, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752047081861294, "dur":57522, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752047081918824, "dur":2795, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752047081921620, "dur":21108, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752047081942748, "dur":77, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/System.Xml-FeaturesChecked.txt_wnnz.info" }}
,{ "pid":12345, "tid":19, "ts":1752047081942827, "dur":114, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752047081942942, "dur":314, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/System.Xml-FeaturesChecked.txt_wnnz.info" }}
,{ "pid":12345, "tid":19, "ts":1752047081943258, "dur":101, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-mdpi/ic_launcher_foreground.png" }}
,{ "pid":12345, "tid":19, "ts":1752047081944936, "dur":8497, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.resource" }}
,{ "pid":12345, "tid":19, "ts":1752047081953481, "dur":13268386, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752047081861373, "dur":57472, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752047081918853, "dur":3104, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752047081921958, "dur":20753, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752047081942727, "dur":120, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.Cinemachine-FeaturesChecked.txt_blar.info" }}
,{ "pid":12345, "tid":20, "ts":1752047081942848, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752047081942926, "dur":245, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.Cinemachine-FeaturesChecked.txt_blar.info" }}
,{ "pid":12345, "tid":20, "ts":1752047081943248, "dur":67, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/jniLibs/arm64-v8a/libmain.so" }}
,{ "pid":12345, "tid":20, "ts":1752047081943374, "dur":51, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"WriteText D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/colors.xml" }}
,{ "pid":12345, "tid":20, "ts":1752047081943427, "dur":168, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/colors.xml" }}
,{ "pid":12345, "tid":20, "ts":1752047081943597, "dur":13278585, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752047081861458, "dur":57413, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752047081918876, "dur":2830, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752047081921706, "dur":20999, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752047081942745, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752047081942814, "dur":63, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752047081942877, "dur":490, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.XRModule-FeaturesChecked.txt_xks3.info" }}
,{ "pid":12345, "tid":21, "ts":1752047081943370, "dur":63, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"WriteText D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/values/strings.xml" }}
,{ "pid":12345, "tid":21, "ts":1752047081943521, "dur":123, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values-v28/styles.xml" }}
,{ "pid":12345, "tid":21, "ts":1752047081943647, "dur":13278383, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752047081861577, "dur":57309, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752047081918891, "dur":2579, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752047081921471, "dur":1860, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752047081923331, "dur":19427, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752047081942761, "dur":119, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/System.Configuration-FeaturesChecked.txt_vfp9.info" }}
,{ "pid":12345, "tid":22, "ts":1752047081942882, "dur":151, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752047081943197, "dur":195, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ActionGenerateProjectFiles Library/Bee/artifacts/Android/IntermediateFiles.txt (+44 others)" }}
,{ "pid":12345, "tid":22, "ts":1752047081943431, "dur":687, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752047081944128, "dur":13277839, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752047081861636, "dur":57359, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752047081918999, "dur":2430, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752047081921430, "dur":1805, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752047081923236, "dur":19449, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752047081942690, "dur":93, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.AccessibilityModule-FeaturesChecked.txt_ttts.info" }}
,{ "pid":12345, "tid":23, "ts":1752047081942964, "dur":347, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.TextRenderingModule-FeaturesChecked.txt_ighi.info" }}
,{ "pid":12345, "tid":23, "ts":1752047081943313, "dur":114, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-anydpi-v26/app_icon_round.xml" }}
,{ "pid":12345, "tid":23, "ts":1752047081943492, "dur":53, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"WriteText D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/resources/META-INF/com.android.games.engine.build_fingerprint" }}
,{ "pid":12345, "tid":23, "ts":1752047081943547, "dur":126, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/resources/META-INF/com.android.games.engine.build_fingerprint" }}
,{ "pid":12345, "tid":23, "ts":1752047081943675, "dur":13278427, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752047081861715, "dur":57329, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752047081919045, "dur":2502, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752047081921547, "dur":21126, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752047081942797, "dur":56, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752047081942853, "dur":360, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/DOTween-FeaturesChecked.txt_esvd.info" }}
,{ "pid":12345, "tid":24, "ts":1752047081943215, "dur":80, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/java/com/unity3d/player/UnityPlayerGameActivity.java" }}
,{ "pid":12345, "tid":24, "ts":1752047081943335, "dur":123, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/freeformwindow.xml" }}
,{ "pid":12345, "tid":24, "ts":1752047081943458, "dur":57, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/freeformwindow.xml" }}
,{ "pid":12345, "tid":24, "ts":1752047081943614, "dur":105, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/RuntimeInitializeOnLoads.json" }}
,{ "pid":12345, "tid":24, "ts":1752047081943722, "dur":13278335, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752047095243603, "dur":4452, "ph":"X", "name": "ProfilerWriteOutput" }
,