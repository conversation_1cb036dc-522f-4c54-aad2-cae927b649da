{ "pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "netcorerun.dll" } },
{ "pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "-1" } },
{ "pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 35942, "tid": 1, "ts": 1752047080527449, "dur": 1106741, "ph": "X", "name": "BuildProgram", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1752047080529407, "dur": 83942, "ph": "X", "name": "BuildProgramContextConstructor", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1752047080647583, "dur": 923070, "ph": "X", "name": "RunBuildProgram", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1752047080649332, "dur": 749943, "ph": "X", "name": "PlayerBuildProgramBase.SetupPlayerBuild", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1752047080651068, "dur": 96449, "ph": "X", "name": "SetupDataFiles", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1752047080748220, "dur": 1043, "ph": "X", "name": "SetupCopyPlugins", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1752047080753171, "dur": 135864, "ph": "X", "name": "SetupUnityLinker", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1752047080890246, "dur": 496272, "ph": "X", "name": "SetupIl2CppBuild", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1752047080891505, "dur": 113875, "ph": "X", "name": "SetupIl2Cpp", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1752047081118406, "dur": 253699, "ph": "X", "name": "SetupSpecificConfigImpl libil2cpp", "args": {"info": "release_Android_arm64"} },
{ "pid": 35942, "tid": 1, "ts": 1752047081132461, "dur": 168431, "ph": "X", "name": "SetupSpecificConfigImpl il2cpp", "args": {"info": "release_Android_arm64"} },
{ "pid": 35942, "tid": 1, "ts": 1752047081135055, "dur": 90120, "ph": "X", "name": "SetupSpecificConfigImpl bdwgc", "args": {"info": "release_Android_arm64"} },
{ "pid": 35942, "tid": 1, "ts": 1752047081225221, "dur": 4886, "ph": "X", "name": "SetupSpecificConfigImpl zlib", "args": {"info": "release_Android_arm64"} },
{ "pid": 35942, "tid": 1, "ts": 1752047081387404, "dur": 11869, "ph": "X", "name": "SetupCopyDataIl2cpp", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1752047081583445, "dur": 2257, "ph": "X", "name": "OutputData.Write", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1752047081585704, "dur": 48478, "ph": "X", "name": "Backend.Write", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1752047081587399, "dur": 38882, "ph": "X", "name": "JsonToString", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1752047081655024, "dur": 6235, "ph": "X", "name": "", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1752047081653727, "dur": 8284, "ph": "X", "name": "Write chrome-trace events", "args": {} },
