{ "pid": 36672, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "<PERSON><PERSON><PERSON><PERSON>" } },
{ "pid": 36672, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "0" } },
{ "pid": 36672, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": { "name": "GC - Gen2" } },
{ "pid": 36672, "tid": 12884901888, "ts": 1752047086833299, "dur": 384747, "ph": "X", "name": "GC - Gen 2", "args": {"Reason": "LOH allocation", "Type": "Background GC"} },
{ "pid": 36672, "tid": 1, "ts": 1752047095054790, "dur": 4983, "ph": "X", "name": "GC - Gen2", "args": {} },
{ "pid": 36672, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": { "name": "GC - Gen1" } },
{ "pid": 36672, "tid": 1, "ts": 1752047095059776, "dur": 1, "ph": "X", "name": "GC - Gen1", "args": {} },
{ "pid": 36672, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": { "name": "GC - Gen0" } },
{ "pid": 36672, "tid": 4294967296, "ts": 1752047086855337, "dur": 197865, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC during background GC"} },
{ "pid": 36672, "tid": 1, "ts": 1752047095059778, "dur": 102, "ph": "X", "name": "GC - Gen0", "args": {} },
{ "pid": 36672, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 36672, "tid": 1, "ts": 1752047082427719, "dur": 12599179, "ph": "X", "name": "UnityLinker.exe", "args": {"analytics": "1"} },
{ "pid": 36672, "tid": 1, "ts": 1752047082430976, "dur": 121342, "ph": "X", "name": "InitAndSetup", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047082440611, "dur": 68242, "ph": "X", "name": "PrepareInstances", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047082552321, "dur": 21208, "ph": "X", "name": "RegisterRuntimeEventListeners", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047082621416, "dur": 69344, "ph": "X", "name": "ParseArguments", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047082694732, "dur": 188497, "ph": "X", "name": "SetupAndRegisterUnityEngineSteps", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047082883321, "dur": 53814, "ph": "X", "name": "ResolveUnityEngine", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047082937159, "dur": 208473, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047083145655, "dur": 930062, "ph": "X", "name": "InitializeEngineStrippingStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047084075746, "dur": 140924, "ph": "X", "name": "ResolveForEngineModuleStrippingDisabledStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047084216691, "dur": 90530, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047084307229, "dur": 3257, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047084310489, "dur": 6031, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047084316524, "dur": 759, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047084317303, "dur": 25954, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047084343266, "dur": 617, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047084343887, "dur": 1067, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047084344957, "dur": 402, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047084345362, "dur": 70995, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047084416379, "dur": 328564, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047084744958, "dur": 999, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047084745962, "dur": 778, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047084746745, "dur": 369411, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047085116167, "dur": 1763, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047085117934, "dur": 1946, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047085119900, "dur": 21365, "ph": "X", "name": "SetupAndRegisterUnityRootsSteps", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047085141293, "dur": 12674, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047085153978, "dur": 5740, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047085159721, "dur": 3848, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047085163572, "dur": 9928, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047085173508, "dur": 8770, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047085182284, "dur": 3636, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047085185923, "dur": 2140, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047085188066, "dur": 2521, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047085190590, "dur": 2006, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047085192639, "dur": 3827, "ph": "X", "name": "UnityLoadReferencesStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047085196478, "dur": 968, "ph": "X", "name": "SetupAndRegisterUnityRootsSteps2", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047085197456, "dur": 1933, "ph": "X", "name": "SetupI18N", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047085199402, "dur": 8442, "ph": "X", "name": "ResolveFromDescriptorsStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047085207849, "dur": 213, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047085208064, "dur": 264, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047085208330, "dur": 5401, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047085213736, "dur": 3717, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047085217457, "dur": 1844, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047085219315, "dur": 43276, "ph": "X", "name": "UnityBlacklistStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047085262599, "dur": 13594, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047085276225, "dur": 240280, "ph": "X", "name": "DynamicDependencyLookupStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047085516536, "dur": 944, "ph": "X", "name": "EarlyReportGenerationStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047085517488, "dur": 975, "ph": "X", "name": "ResolveTestsStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047085518474, "dur": 528, "ph": "X", "name": "LoadI18nAssemblies", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047085519011, "dur": 1768, "ph": "X", "name": "ResolveMonoBehaviourItselfStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047085520789, "dur": 1212, "ph": "X", "name": "ResolveFromAllUserMonoBehaviours", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047085522010, "dur": 5899, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047085527913, "dur": 235, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047085528150, "dur": 247, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047085528398, "dur": 122, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047085528521, "dur": 15, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047085528549, "dur": 152986, "ph": "X", "name": "ResolveFromPreserveAttribute", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047085681559, "dur": 270469, "ph": "X", "name": "UnityTypeMapStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047085952048, "dur": 508, "ph": "X", "name": "BeforeMarkReportGenerationStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047085952563, "dur": 207857, "ph": "X", "name": "BeforeMarkAnalyticsStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047086160439, "dur": 66345, "ph": "X", "name": "RemoveSecurityStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047086226812, "dur": 12099, "ph": "X", "name": "RemoveSecurityFromCopyAssemblies", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047086238937, "dur": 38512, "ph": "X", "name": "RemoveFeaturesStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047086277478, "dur": 38495, "ph": "X", "name": "RemoveUnreachableBlocksStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047086315997, "dur": 4654763, "ph": "X", "name": "UnityMarkStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047090970780, "dur": 1395, "ph": "X", "name": "ValidateVirtualMethodAnnotationsStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047090972182, "dur": 10033, "ph": "X", "name": "ProcessWarningsStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047090982221, "dur": 127951, "ph": "X", "name": "UnitySweepStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047091110190, "dur": 11018, "ph": "X", "name": "UnityCodeRewriterStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047091121220, "dur": 9697, "ph": "X", "name": "CleanStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047091130928, "dur": 5879, "ph": "X", "name": "StubifyStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047091136814, "dur": 6995, "ph": "X", "name": "AddUnresolvedStubsStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047091143818, "dur": 8134, "ph": "X", "name": "BeforeOutputAnalyticsStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047091151960, "dur": 364, "ph": "X", "name": "BeforeOutputReportGenerationStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047091152332, "dur": 892, "ph": "X", "name": "ProcessCopiedAssembliesRequiringSecurityAttributesRemoved", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047091153233, "dur": 997, "ph": "X", "name": "SealerStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047091154249, "dur": 3778010, "ph": "X", "name": "UnityOutputStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047094932284, "dur": 39244, "ph": "X", "name": "LinkerToEditorDataGenerationStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047094971539, "dur": 424, "ph": "X", "name": "ReportGenerationStep", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047094976775, "dur": 49777, "ph": "X", "name": "Analytics", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047095026900, "dur": 14159, "ph": "X", "name": "UnregisterRuntimeEventListeners", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047095059882, "dur": 232, "ph": "X", "name": "", "args": {} },
{ "pid": 36672, "tid": 1, "ts": 1752047095053744, "dur": 7045, "ph": "X", "name": "Write chrome-trace events", "args": {} },
